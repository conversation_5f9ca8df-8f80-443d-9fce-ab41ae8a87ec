=== Auto Verify Pending Payment ===
Contributors: piprapay
Donate link: https://piprapay.com/donate
Tags: transaction, automation
Requires at least: 1.0.0
Tested up to: 1.0.0
Stable tag: 1.0.0
License: GPL-2.0+
License URI: https://www.gnu.org/licenses/gpl-2.0.html

== Description ==

Automatically verifies pending payments from bKash, Nagad, Rocket, Upay, and other Bangladeshi MFS. If a user’s payment couldn't be verified instantly (e.g., due to no internet), once the payment SMS is received later, the plugin checks the transaction and confirms it automatically—no manual action needed.

**Key Features:**


**Use Cases:**

== Changelog ==

= 1.0.0 =
* Initial release